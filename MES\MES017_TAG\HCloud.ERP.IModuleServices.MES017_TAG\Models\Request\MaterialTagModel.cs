﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HCloud.ERP.IModuleServices.MES017_TAG.Models.Request
{
    public class MaterialTagModel
    {
        /// <summary>
        /// 物料名称
        /// </summary>
        public string FMATERIAL_NAME { get; set; }
        /// <summary>
        /// 物料代号
        /// </summary>
        public string FMATERIAL_CODE { get; set; }
        public string MATERIAL_ID { get; set; }
        /// <summary>
        /// 仓库单位名称
        /// </summary>
        public string FSTK_UNIT_NAME { get; set; }

        /// <summary>
        /// 扫描数
        /// </summary>
        public decimal? FSCAN_NUM { get; set; } = 0m;
        /// <summary>
        /// 入库总量
        /// </summary>
        public decimal? FNUM { get; set; } = 0m;
        /// <summary>
        /// 标签总数
        /// </summary>
        public int TagNum { get; set; }
        /// <summary>
        /// 标签扫描数
        /// </summary>
        public int TagScanNum { get; set; }
        /// <summary>
        /// 生成ID
        /// </summary>
        public string FTAG_GEN_LOG_ID { get; set; }
        /// <summary>
        /// 单据来源单号
        /// </summary>
        public string? FBILL_SOURCE_NO { get; set; }


        /// <summary>
        /// 单据标签类型
        /// </summary>
        public string? FBILL_TYPE { get; set; }
        /// <summary>
        /// 单据来源细表ID
        /// </summary>
        public string? FBILL_SOURCE_ITEM_ID { get; set; }
        /// <summary>
        /// 库存单位ID
        /// </summary>
        public string? FSTK_UNIT_ID { get; set; }

        public List<OrderTagRkModel> child { get; set; }

        public List<stockTagList>? STOCKLIST { get; set; }

        public string FSPEC_DESC { get; set; }
        public string FSTORE_ID { get; set; }
        public string FSTORE_NAME { get; set; }
        public string FSTORE_PLACE_ID { get; set; }
        public string FSTORE_PLACE_NAME { get; set; }
        public string FTAG_CODE_TYPE { get; set; }
        public decimal? FSTK_QTY { get; set; } = 0m;
    }

  
}
