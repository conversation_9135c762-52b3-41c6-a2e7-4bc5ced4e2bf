﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net5.0</TargetFramework>
		<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
		<AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<OutputPath>F:\modules\mes-system-web\Mesroot\MicroServer\Modules</OutputPath>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="HCloud.Core.HCPlatform" Version="1.0.113" />
		<PackageReference Include="HCloud.Core.ProxyGenerator" Version="1.0.30" />
		<PackageReference Include="QRCoder" Version="1.6.0" />
		<PackageReference Include="Spire.XLS" Version="13.5.6" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Properties\" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\HCloud.ERP.IModuleServices.MES017_TAG\HCloud.ERP.IModuleServices.MES017_TAG.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <Reference Include="HCloud.Core.Auth">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\HCloud.Core.Auth.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.Core.Caching">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\HCloud.Core.Caching.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.Core.Common">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.Core.Common.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.Core.KestrelHttpServer">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.Core.KestrelHttpServer.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.Core.Nlog">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\HCloud.Core.Nlog.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.Core.Quartz">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\HCloud.Core.Quartz.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.Core.Stage">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\HCloud.Core.Stage.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.Core.Sugar">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.Core.Sugar.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.Core.Swagger">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\HCloud.Core.Swagger.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.Core.System">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\HCloud.Core.System.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.ADM024_Employee">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.ADM024_Employee.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.COP002_SaleDlv">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.COP002_SaleDlv.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.MES003_WorkOrder">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.MES003_WorkOrder.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.COP001_SaleOrder">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.COP001_SaleOrder.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.MES005_CraftSchedule">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.MES005_CraftSchedule.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.MES007_JobBooking">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.MES007_JobBooking.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.MES016_WoRec">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.MES016_WoRec.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.MSD001_Unit">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.MSD001_Unit.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.MSD002_Material">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.MSD002_Material.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.MSD004_MaterialBOM">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.MSD004_MaterialBOM.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.MSD005_TranEntity">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.MSD005_TranEntity.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.PUR011_OutOrder">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.PUR011_OutOrder.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.PUR014_OutSend">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.PUR014_OutSend.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.PUR015_OutCraftRecv">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.PUR015_OutCraftRecv.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.PUR021_OutOrderStock">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.PUR021_OutOrderStock.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.PUR024_SupplierDeliveryOrder">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.PUR024_SupplierDeliveryOrder.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.STK001_Store">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.STK001_Store.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.STK002_StorePlace">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.STK002_StorePlace.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.STK005_Stock">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.STK005_Stock.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.SYS010_DataImportExport">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.SYS010_DataImportExport.dll</HintPath>
	  </Reference>
	  <Reference Include="HCServer">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\HCServer.dll</HintPath>
	  </Reference>
	</ItemGroup>

</Project>
