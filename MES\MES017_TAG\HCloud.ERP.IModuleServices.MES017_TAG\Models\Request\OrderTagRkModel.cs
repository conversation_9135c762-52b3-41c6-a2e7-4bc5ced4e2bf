﻿using HCloud.Core.HCPlatform.Sugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HCloud.ERP.IModuleServices.MES017_TAG.Models.Request
{
    public class OrderTagRkModel
    {
        /// <summary>
        /// 单据标签ID
        /// </summary>
        public string FTAG_GEN_LOG_ID { get; set; }

        /// <summary>
        /// 单据来源ID
        /// </summary>
        public string? FBILL_SOURCE_ID { get; set; }

        /// <summary>
        /// 单据来源代号
        /// </summary>
        public string? FBILL_SOURCE_NO { get; set; }

        /// <summary>
        /// 单据来源细表ID
        /// </summary>
        public string? FBILL_SOURCE_ITEM_ID { get; set; }

        /// <summary>
        /// BOM ID
        /// </summary>
        public string? FBOM_ID { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        public string? MATERIAL_ID { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal? FNUM { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal? FSTK_QTY { get; set; }
        /// <summary>
        /// 单据类型
        /// </summary>
        public string? FBILL_TYPE { get; set; }
        /// <summary>
        /// 标签类型
        /// </summary>
        public string? FTAG_CODE_TYPE { get; set; }

     

        /// <summary>
        /// 库存单位
        /// </summary>
        public string? FSTK_UNIT_ID { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? FREMARK { get; set; }


        public string? FMATERIAL_NAME { get; set; }

        public string? FMATERIAL_CODE { get; set; }

        public List<stockTagList>? STOCKLIST { get; set; }

        public string FSTK_UNIT_NAME { get; set; }
        public string FSPEC_DESC { get; set; }
        public string FSTORE_ID { get; set; }
        public string FSTORE_NAME { get; set; }
        public string FSTORE_PLACE_ID { get; set; }
        public string FSTORE_PLACE_NAME { get; set; }
        public decimal? FSCAN_NUM { get; set; } = 0;

        /// <summary>
        ///数量
        /// </summary>
        public decimal? FPRODUCT_NUM { get; set; }
        /// <summary>
        /// 重量
        /// </summary>
        public decimal? FPRODUCT_WEIGHT { get; set; }
        /// <summary>
        /// 毛重
        /// </summary>
        public decimal? FGROSS_WEIGHT { get; set; }
        /// <summary>
        /// 材料批号
        /// </summary>
        public string FSTUFFLOT_NO
        {
            get; set;
        }
        /// <summary>
        /// 条码号
        /// </summary>
        public string FBARCODE_NO { get; set; }
        public string FTEXT1 { get; set; }
        public string FTEXT2 { get; set; }
        public string FTEXT3 { get; set; }
        public string FTEXT4 { get; set; }
        public string FTEXT5 { get; set; }
        public string FSHOW_SEQNO { get; set; }
        /// <summary>
        /// 打印标签类型CraftTagType子表，内外包箱,产品(ITEM,PACK,PRODCUT)
        /// </summary>
        public string FTAG_TYPE { get; set; }
        /// <summary>
        /// 包装类型（ITEM,PACK），内包箱,外包箱
        /// </summary>
        public string FPACK_TYPE { get; set; }
        /// <summary>
        /// 上级包箱号
        /// </summary>
        public string FPACK_PARENTS { get; set; }
        /// <summary>
        /// 是否尾箱（true=尾箱，false=整箱）
        /// </summary>
        public bool? FIS_TAIL_BOX { get; set; }



        // <summary>
        /// 使用状态  枚举值：SCANNED, INVALID, REUSED
        /// </summary>
        public string? FUSE_STATUS { get; set; }

        /// <summary>
        /// 扫描时间
        /// </summary>
        public DateTime? FSCAN_TIME { get; set; }

        /// <summary>
        /// 扫描来源  例如：APP、PDA、WEB
        /// </summary>
        public string? FSCAN_SOURCE { get; set; }

        /// <summary>
        /// 扫描结果  扫描后的业务处理结果或状态说明
        /// </summary>
        public string? FSCAN_RESULT { get; set; }

       

        /// <summary>
        /// 扫描状态(-1失败，1.暂存，2.提交成功)
        /// </summary>
        public int? FSCAN_TYPE { get; set; }
        /// <summary>
        /// 扫描来源单据
        /// </summary>
        public string? FSCAN_SOURCE_NO { get; set; }
       
        /// <summary>
        /// 库存状态1.已入库，2.已出库
        /// </summary>
        public int? FSTORE_TYPE { get; set; }
        /// <summary>
        /// 入库量
        /// </summary>
        public decimal? FSTORE_NUM { get; set; } = 1m;

        public string FSCAN_TYPE_NAME { get {
                if (FSCAN_TYPE == 1)
                {
                    return "暂存";
                }
                else if (FSCAN_TYPE == 2)
                {
                    return "提交成功";
                }
                else if (FSCAN_TYPE == -1)
                {
                    return "失败";
                }
                else { 
                    return "未扫描";
                }

            } }
    }

    public class stockTagList
    {
        public string STOCK_NAME { get; set; }
        public string STOCK_CODE { get; set; }
        public string STOCK_ID { get; set; }

    }
}
