﻿using HCloud.Core.Common;
using HCloud.Core.HCPlatform.Auth.Models;
using HCloud.Core.HCPlatform.MR;
using HCloud.Core.ProxyGenerator;
using HCloud.Core.Sugar;
using HCloud.ERP.IModuleServices.MES003_WorkOrder.Models;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models.Response;
using HCloud.ERP.IModuleServices.MES017_TAG;
using HCloud.ERP.IModuleServices.MES017_TAG.Models.Response;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HCloud.ERP.ModuleServices.MES007_JobBooking
{
    public partial class MES007JobBookingService
    {
        /// <summary>
        /// 处理批量拆分完工时的标签逻辑
        /// </summary>
        /// <param name="splitModels">拆分模型列表</param>
        /// <param name="splitResults">拆分结果列表</param>
        /// <param name="jobsToInsert">待插入的报工记录列表</param>
        /// <param name="db">数据库连接</param>
        /// <returns></returns>
        public async Task HandleBatchSplitTagsAsync(
            List<JobBookingOperateModel> splitModels,
            List<BatchSplitResultModel> splitResults,
            List<T_MESD_CRAFT_JOB_BOOKING> jobsToInsert,
            ISqlSugarClient db)
        {
            var user = await _iauth.GetUserAccountAsync();
            var curDate = _iauth.GetCurDateTime();

            foreach (var model in splitModels)
            {
                if (model.tagItems != null && model.tagItems.Count > 0)
                {
                    var Ins = model.tagItems.Where(n => (n.FCRAFT_JOB_BOOKING_TAG_ID == null || n.FCRAFT_JOB_BOOKING_TAG_ID == "") && n.FPRODUCT_NUM > 0 && n.FPRODUCT_WEIGHT > 0).ToList();
                    if (Ins != null && Ins.Count > 0)
                    {
                        // 找到对应的完工排程和报工记录
                        var resultItem = splitResults.FirstOrDefault(r => r.FORIGINAL_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID);
                        if (resultItem != null)
                        {
                            var finishedScheduleId = resultItem.FSPLIT_FINISHED_SCHEDULE_ID;
                            var finishedScheduleNo = resultItem.FSPLIT_FINISHED_SCHEDULE_NO;
                            
                            // 找到对应的完工报工记录ID
                            string finishedJobBookingId = null;
                            
                            // 情况一：最终完工 - 使用更新的原报工记录
                            if (finishedScheduleId == model.FCRAFT_SCHEDULE_ID)
                            {
                                finishedJobBookingId = model.FCRAFT_JOB_BOOKING_ID;
                            }
                            // 情况二：部分拆分 - 使用新创建的报工记录
                            else
                            {
                                finishedJobBookingId = jobsToInsert.FirstOrDefault(j => j.FCRAFT_SCHEDULE_ID == finishedScheduleId)?.FCRAFT_JOB_BOOKING_ID;
                            }
                            
                            if (!string.IsNullOrEmpty(finishedJobBookingId))
                            {
                                await ProcessTagsForFinishedJobAsync(Ins, finishedJobBookingId, finishedScheduleId, finishedScheduleNo, model.FMATERIAL_CODE, user, curDate, db);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 为完工的报工记录处理标签
        /// </summary>
        /// <param name="tagItems">标签项列表</param>
        /// <param name="jobBookingId">报工记录ID</param>
        /// <param name="scheduleId">排程ID</param>
        /// <param name="scheduleNo">排程编号</param>
        /// <param name="materialCode">物料编码</param>
        /// <param name="user">用户信息</param>
        /// <param name="curDate">当前时间</param>
        /// <param name="db">数据库连接</param>
        /// <returns></returns>
        private async Task ProcessTagsForFinishedJobAsync(
            List<TagItem> tagItems,
            string jobBookingId,
            string scheduleId,
            string scheduleNo,
            string materialCode,
            UserAccountModel user,
            DateTime curDate,
            ISqlSugarClient db)
        {
            var NewTagList = new List<T_MESD_CRAFT_JOB_BOOKING_TAG>();
            foreach (var item in tagItems)
            {
                NewTagList.Add(new T_MESD_CRAFT_JOB_BOOKING_TAG
                {
                    FCRAFT_JOB_BOOKING_TAG_ID = GuidHelper.NewGuid(),
                    FCRAFT_JOB_BOOKING_ID = jobBookingId,
                    FCRAFT_SCHEDULE_ID = scheduleId,
                    FCREATOR_ID = user.UserId,
                    FCREATOR = user.UserPsnName,
                    FSTUFFLOT_NO = item.FSTUFFLOT_NO,
                    FCDATE = curDate,
                    FECODE = user.CompanyId,
                    FPRODUCT_NUM = item.FPRODUCT_NUM,
                    FPRODUCT_WEIGHT = item.FPRODUCT_WEIGHT,
                    FGROSS_WEIGHT = item.FGROSS_WEIGHT,
                    FTEXT1 = item.FTEXT1,
                    FTEXT2 = item.FTEXT2,
                    FTEXT3 = item.FTEXT3,
                    FTEXT4 = item.FTEXT4,
                    FTEXT5 = item.FTEXT5,
                    FSHOW_SEQNO = item.FSHOW_SEQNO,
                    FTAG_TYPE = "ITEM"
                });
            }

            // 生成标签条码
            var _genlist = new List<TagGenModel>();
            foreach (var item in NewTagList)
            {
                _genlist.Add(new TagGenModel 
                { 
                    GEN_NO = materialCode, 
                    FBILL_SOURCE_ITEM_ID = item.FCRAFT_JOB_BOOKING_TAG_ID, 
                    FBILL_SOURCE_NO = scheduleNo, 
                    FSTK_QTY = item.FPRODUCT_NUM, 
                    FBILL_TYPE = "OrderInScan".ToUpper() 
                });
            }

            var rpcServer = this.GetService<IMES017TagService>("MES017Tag");
            var TagList = await rpcServer.GenerateOrderCodeList(_genlist);
            if (TagList.StatusCode != 200)
            {
                ERROR(null, TagList.StatusCode, TagList.Message);
            }

            NewTagList.ForEach(x => x.FBARCODE_NO = TagList.Entity.FirstOrDefault(n => n.FBILL_SOURCE_ITEM_ID == x.FCRAFT_JOB_BOOKING_TAG_ID)?.FBARCODE_NO);
            await db.Insertable<T_MESD_CRAFT_JOB_BOOKING_TAG>(NewTagList).ExecuteCommandAsync();
        }

        /// <summary>
        /// 根据工单ID列表查询所有标签
        /// </summary>
        /// <param name="workOrderIds">工单ID列表</param>
        /// <returns>标签列表</returns>
        public async Task<DataResult<List<JobBookingTagModel>>> QueryTagsByWoOrdNosAsync(List<string> woOrdNos)
        {
            var db = _isugar.DB;
            var result = new DataResult<List<JobBookingTagModel>> { StatusCode = 200, Entity = new List<JobBookingTagModel>() };

            try
            {
                if (woOrdNos == null || !woOrdNos.Any())
                {
                    result.Message = "工单ID列表不能为空";
                    result.StatusCode = 100001;
                    return await OK(result);
                }

                // 查询标签信息，关联排程任务和工单
                var tagList = (await db.Queryable<T_MESD_CRAFT_JOB_BOOKING_TAG, T_MESD_CRAFT_SCHEDULE, T_MESD_WORK_ORDER>(
                    (tag, schedule, workOrder) => new JoinQueryInfos(
                        JoinType.Inner, tag.FCRAFT_SCHEDULE_ID == schedule.FCRAFT_SCHEDULE_ID,
                        JoinType.Inner, schedule.FWORK_ORDER_ID == workOrder.FWORK_ORDER_ID
                    ))
                    .Where((tag, schedule, workOrder) => woOrdNos.Contains(workOrder.FWORK_ORDER_NO))
                    .Select<JobBookingTagModel>()
                    .ToListAsync()).OrderByDescending(p => p.FBARCODE_NO).ToList();

                if (tagList.Any())
                {
                    // 获取唯一的物料ID列表
                    var materialIds = tagList.Where(x => !string.IsNullOrEmpty(x.MATERIAL_ID)).Select(x => x.MATERIAL_ID).Distinct().ToList();
                    
                    if (materialIds.Any())
                    {
                        // 获取物料信息
                        var materialResult = await _businessService.GetMaterialByIdsAsync(materialIds);
                        if (materialResult.StatusCode == 200 && materialResult.Entity != null)
                        {
                            var materialDict = materialResult.Entity.ToDictionary(m => m.FMATERIAL_ID);
                            
                            // 填充物料信息
                            foreach (var tag in tagList)
                            {
                                if (!string.IsNullOrEmpty(tag.MATERIAL_ID) && materialDict.TryGetValue(tag.MATERIAL_ID, out var material))
                                {
                                    tag.FMATERIAL_CODE = material.FMATERIAL_CODE;
                                    tag.FMATERIAL_NAME = material.FMATERIAL_NAME;
                                    tag.FSPEC_DESC = material.FSPEC_DESC;
                                    tag.FGOODS_MODEL = material.FGOODS_MODEL;
                                }
                                
                                // 生成条码URL
                                if (!string.IsNullOrEmpty(tag.FBARCODE_NO))
                                {
                                    tag.FBARCODE_URL = GenCode(tag.FBARCODE_NO);
                                }
                            }
                        }
                    }
                }

                result.Entity = tagList;
                return await OK(result);
            }
            catch (Exception ex)
            {
                result.StatusCode = 105000;
                result.Message = $"查询标签信息失败：{ex.Message}";
                return await OK(result);
            }
        }

        /// <summary>
        /// 根据工单编号列表查询未绑定标签的排程任务
        /// </summary>
        /// <param name="woOrdNos">工单编号列表</param>
        /// <returns>未绑定标签的标签列表</returns>
        public async Task<DataResult<List<JobBookingTagModel>>> QueryUnboundTagsByWoOrdNosAsync(QueryAvailableTagsModel model)
        {
            var db = _isugar.DB;
            var result = new DataResult<List<JobBookingTagModel>> { StatusCode = 200, Entity = new List<JobBookingTagModel>() };

            try
            {
                if (model == null || model.WoNo == null || !model.WoNo.Any())
                {
                    result.Message = "获取标签的工单号列表(WoNo)不能为空";
                    result.StatusCode = 100001;
                    return await OK(result);
                }

                var usedTagIdSet = new HashSet<string>();
                if (model.BandWoNo != null && model.BandWoNo.Any())
                {
                    var usedTagIds = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_WORK_ORDER>(
                        (schedule, workOrder) => new JoinQueryInfos(
                            JoinType.Inner, schedule.FWORK_ORDER_ID == workOrder.FWORK_ORDER_ID
                        ))
                        .Where((schedule, workOrder) => model.BandWoNo.Contains(workOrder.FWORK_ORDER_NO) && !string.IsNullOrEmpty(schedule.FSCHEDULE_TEXT1))
                        .Select(schedule => schedule.FSCHEDULE_TEXT1)
                        .Distinct()
                        .ToListAsync();

                    usedTagIdSet = new HashSet<string>(usedTagIds);
                }

                var allTagsInRange = (await db.Queryable<T_MESD_CRAFT_JOB_BOOKING_TAG, T_MESD_CRAFT_SCHEDULE, T_MESD_WORK_ORDER>(
                   (tag, schedule, workOrder) => new JoinQueryInfos(
                       JoinType.Inner, tag.FCRAFT_SCHEDULE_ID == schedule.FCRAFT_SCHEDULE_ID,
                       JoinType.Inner, schedule.FWORK_ORDER_ID == workOrder.FWORK_ORDER_ID
                   ))
                   .Where((tag, schedule, workOrder) => model.WoNo.Contains(workOrder.FWORK_ORDER_NO))
                   .Select<JobBookingTagModel>()
                   .ToListAsync());

                var availableTags = allTagsInRange
                  .Where(tag => !usedTagIdSet.Contains(tag.FCRAFT_JOB_BOOKING_TAG_ID))
                  .OrderByDescending(p => p.FBARCODE_NO)
                  .ToList();

                if (availableTags.Any())
                {
                    // 获取唯一的物料ID列表
                    var materialIds = availableTags.Where(x => !string.IsNullOrEmpty(x.MATERIAL_ID)).Select(x => x.MATERIAL_ID).Distinct().ToList();
                    
                    if (materialIds.Any())
                    {
                        // 获取物料信息
                        var materialResult = await _businessService.GetMaterialByIdsAsync(materialIds);
                        if (materialResult.StatusCode == 200 && materialResult.Entity != null)
                        {
                            var materialDict = materialResult.Entity.ToDictionary(m => m.FMATERIAL_ID);
                            
                            // 填充物料信息
                            foreach (var tag in availableTags)
                            {
                                if (!string.IsNullOrEmpty(tag.MATERIAL_ID) && materialDict.TryGetValue(tag.MATERIAL_ID, out var material))
                                {
                                    tag.FMATERIAL_CODE = material.FMATERIAL_CODE;
                                    tag.FMATERIAL_NAME = material.FMATERIAL_NAME;
                                    tag.FSPEC_DESC = material.FSPEC_DESC;
                                    tag.FGOODS_MODEL = material.FGOODS_MODEL;
                                }
                            }
                        }
                    }
                }

                result.Entity = availableTags;
                return await OK(result);
            }
            catch (Exception ex)
            {
                result.StatusCode = 105000;
                result.Message = $"查询未绑定标签信息失败：{ex.Message}";
                return await OK(result);
            }
        }

    }
}
